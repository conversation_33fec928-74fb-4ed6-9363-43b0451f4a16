<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转正述职报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            text-align: center;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 60px 40px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1em;
            opacity: 0.8;
        }

        .content {
            padding: 50px 40px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .nav-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .nav-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #4facfe;
        }

        .nav-card:nth-child(1) {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        }

        .nav-card:nth-child(2) {
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
        }

        .nav-card:nth-child(3) {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        }

        .nav-card:nth-child(4) {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
        }

        .nav-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .nav-card:first-child .nav-icon {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .nav-card:last-child .nav-icon {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .nav-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .nav-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .nav-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .nav-card:hover .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scale(1.05);
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            color: #6c757d;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2em;
            }

            .header p {
                font-size: 1.1em;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>转正述职报告</h1>
            <p>技术专家 · 团队管理 · 产品创新</p>
            <div class="subtitle">从技术积累到业务价值的完整展示</div>
        </div>

        <div class="content">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.8em;">报告内容导航</h2>

            <div class="nav-grid">
                <a href="personal-introduction.html" class="nav-card">
                    <div class="nav-icon">👨‍💻</div>
                    <div class="nav-title">个人介绍</div>
                    <div class="nav-description">
                        教育背景、核心优势及15年完整工作经历展示，
                        从新浪工程师到AIGC技术负责人的职业发展轨迹
                    </div>
                    <div class="nav-button">查看详情</div>
                </a>

                <a href="probation-work-review.html" class="nav-card">
                    <div class="nav-icon">🚀</div>
                    <div class="nav-title">试用期工作回顾</div>
                    <div class="nav-description">
                        试用期6大重点项目成果展示，涵盖AIGC、RPA、
                        效能平台等多个技术领域的实践与创新
                    </div>
                    <div class="nav-button">查看详情</div>
                </a>

                <a href="work-highlights-and-plans.html" class="nav-card">
                    <div class="nav-icon">⭐</div>
                    <div class="nav-title">工作亮点与未来规划</div>
                    <div class="nav-description">
                        深度复盘工作亮点与不足，价值观践行实例，
                        以及下一步工作计划和个人成长规划
                    </div>
                    <div class="nav-button">查看详情</div>
                </a>

                <a href="work-plan-presentation.html" class="nav-card">
                    <div class="nav-icon">📊</div>
                    <div class="nav-title">工作计划 PPT</div>
                    <div class="nav-description">
                        M78、AI Doc、压测平台三大核心领域的
                        战略规划与技术路线图，圆形图可视化展示
                    </div>
                    <div class="nav-button">查看详情</div>
                </a>
            </div>
        </div>

        <div class="footer">
            <p>📅 转正述职报告 | 🎯 技术创新与业务价值并重</p>
        </div>
    </div>
</body>
</html>
