<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人介绍 - 转正述职</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        /* 教育背景样式 */
        .education-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 2px dashed #2196f3;
            max-width: 300px;
            margin: 0 auto;
        }

        .university {
            font-size: 1.4em;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 8px;
        }

        .major {
            color: #424242;
            font-size: 1em;
            margin-bottom: 10px;
        }

        .duration {
            background: #2196f3;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
        }

        /* 时间轴样式 */
        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            margin-bottom: 40px;
        }

        .timeline-line {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%);
            border-radius: 2px;
            z-index: 1;
        }

        .timeline-dates {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .timeline-date {
            background: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            color: #666;
            border: 2px solid #e0e0e0;
            font-weight: 500;
        }

        .timeline-cards {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            position: relative;
            z-index: 2;
        }

        .timeline-card {
            flex: 1;
            min-height: 180px;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px dashed;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .timeline-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .timeline-card:nth-child(1) {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-color: #ff9800;
        }

        .timeline-card:nth-child(2) {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-color: #9c27b0;
        }

        .timeline-card:nth-child(3) {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-color: #f44336;
        }

        .timeline-card:nth-child(4) {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border-color: #ffc107;
        }

        .timeline-card:nth-child(5) {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-color: #4caf50;
        }

        .timeline-card:nth-child(6) {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #2196f3;
        }

        .company-name {
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .job-position {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 12px;
        }

        .job-description {
            font-size: 0.8em;
            color: #555;
            line-height: 1.4;
        }

        /* 核心优势样式 */
        .strengths {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .strengths h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
            text-align: center;
        }

        .strength-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .strength-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .strength-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #fff;
            font-size: 1.1em;
        }

        .strength-desc {
            font-size: 0.9em;
            opacity: 0.9;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .timeline-cards {
                flex-direction: column;
                gap: 20px;
            }

            .timeline-dates {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .timeline-line {
                display: none;
            }

            .strength-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>个人介绍</h1>
            <p>转正述职报告</p>
        </div>

        <div class="content">
            <!-- 教育背景 -->
            <div class="section">
                <h2 class="section-title">教育背景</h2>
                <div class="education-card">
                    <div class="university">中南民族大学</div>
                    <div class="major">信息与计算科学专业 · 本科</div>
                    <div class="duration">2004.09 - 2008.06</div>
                </div>
            </div>

            <!-- 核心优势 -->
            <div class="section">
                <div class="strengths">
                    <h3>核心优势</h3>
                    <div class="strength-grid">
                        <div class="strength-item">
                            <div class="strength-title">架构与技术能力</div>
                            <div class="strength-desc">精通高并发、大访问量系统设计，拥有大厂背景和丰富架构经验</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">团队管理能力</div>
                            <div class="strength-desc">成功管理30+技术团队，拥有行之有效的管理方法论</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">产品创新能力</div>
                            <div class="strength-desc">敏锐的产品sense，通过数据驱动精准把握需求</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">AIGC技术应用</div>
                            <div class="strength-desc">在数字人、大模型微调、RAG等领域有丰富项目经验</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工作经历时间轴 -->
            <div class="section">
                <h2 class="section-title">工作经历</h2>
                <div class="timeline">
                    <div class="timeline-dates">
                        <div class="timeline-date">~ 2008年7月</div>
                        <div class="timeline-date">~ 2010年3月</div>
                        <div class="timeline-date">~ 2015年11月</div>
                        <div class="timeline-date">~ 2016年8月</div>
                        <div class="timeline-date">~ 2017年11月</div>
                        <div class="timeline-date">~ 至今</div>
                    </div>

                    <div class="timeline-line"></div>

                    <div class="timeline-cards">
                        <div class="timeline-card">
                            <div class="company-name">新浪</div>
                            <div class="job-position">工程师</div>
                            <div class="job-description">
                                <strong>技术基础积累：</strong>高并发系统开发<br>
                                <strong>业务贡献：</strong>论坛2.0日访问量3000万<br>
                                <strong>项目成果：</strong>获新浪最佳项目奖
                            </div>
                        </div>

                        <div class="timeline-card">
                            <div class="company-name">百度</div>
                            <div class="job-position">架构师</div>
                            <div class="job-description">
                                <strong>架构能力：</strong>音乐平台千万级架构设计<br>
                                <strong>技术创新：</strong>反作弊算法模型，获专利<br>
                                <strong>稳定性：</strong>服务可用性提升至99.9%
                            </div>
                        </div>

                        <div class="timeline-card">
                            <div class="company-name">明星衣橱</div>
                            <div class="job-position">技术总监</div>
                            <div class="job-description">
                                <strong>团队管理：</strong>50+技术团队管理<br>
                                <strong>系统重构：</strong>微服务架构升级<br>
                                <strong>业务优化：</strong>丢单率从15%降至1%
                            </div>
                        </div>

                        <div class="timeline-card">
                            <div class="company-name">太合音乐</div>
                            <div class="job-position">高级架构师</div>
                            <div class="job-description">
                                <strong>架构优化：</strong>音乐平台性能提升<br>
                                <strong>数据建模：</strong>版权价值链分析平台<br>
                                <strong>成本控制：</strong>版权采购决策优化
                            </div>
                        </div>

                        <div class="timeline-card">
                            <div class="company-name">伟东云教育</div>
                            <div class="job-position">技术总监</div>
                            <div class="job-description">
                                <strong>团队建设：</strong>30+技术团队，技术战略<br>
                                <strong>平台建设：</strong>云学堂千万用户，百万并发<br>
                                <strong>技术创新：</strong>知识图谱+RAG智能系统
                            </div>
                        </div>

                        <div class="timeline-card">
                            <div class="company-name">亚思未来科技</div>
                            <div class="job-position">大模型负责人</div>
                            <div class="job-description">
                                <strong>AIGC架构：</strong>电商直播技术方案<br>
                                <strong>模型优化：</strong>小语种模型超越GPT-4<br>
                                <strong>产品落地：</strong>数字人直播解决方案
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
