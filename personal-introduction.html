<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人介绍 - 转正述职</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #4facfe;
            display: inline-block;
        }

        .education-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #4facfe;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .university {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
        }

        .major {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .duration {
            background: #4facfe;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .experience-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #e74c3c;
            transition: transform 0.3s ease;
        }

        .experience-item:hover {
            transform: translateY(-5px);
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .job-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .company {
            color: #e74c3c;
            font-weight: 500;
        }

        .keywords {
            background: #ecf0f1;
            color: #7f8c8d;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            margin-bottom: 10px;
            display: inline-block;
        }

        .job-duration {
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            white-space: nowrap;
        }

        .achievements {
            list-style: none;
            margin-top: 15px;
        }

        .achievements li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            color: #555;
        }

        .achievements li:before {
            content: "▸";
            color: #4facfe;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .strengths {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .strengths h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .strength-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .strength-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .strength-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #fff;
        }

        .strength-desc {
            font-size: 0.9em;
            opacity: 0.9;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .experience-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .job-duration {
                margin-top: 10px;
            }
            
            .education-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .duration {
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>个人介绍</h1>
            <p>转正述职报告</p>
        </div>

        <div class="content">
            <!-- 教育背景 -->
            <div class="section">
                <h2 class="section-title">教育背景</h2>
                <div class="education-card">
                    <div class="education-header">
                        <div>
                            <div class="university">中南民族大学</div>
                            <div class="major">信息与计算科学专业 · 本科</div>
                        </div>
                        <div class="duration">2004.09 - 2008.06</div>
                    </div>
                </div>
            </div>

            <!-- 个人优势 -->
            <div class="section">
                <div class="strengths">
                    <h3>核心优势</h3>
                    <div class="strength-grid">
                        <div class="strength-item">
                            <div class="strength-title">丰富的行业经验与架构能力</div>
                            <div class="strength-desc">多年互联网行业从业经历，精通高并发、大访问量和系统稳定性等复杂业务场景的解决方案</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">团队管理与领导力</div>
                            <div class="strength-desc">在创业环境中成功组建并管理团队，拥有一套行之有效的管理方法论</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">产品创新与数据驱动能力</div>
                            <div class="strength-desc">拥有敏锐的产品sense和强烈的创新意识，通过数据挖掘精准把握产品需求</div>
                        </div>
                        <div class="strength-item">
                            <div class="strength-title">广泛的行业经验与新技术应用</div>
                            <div class="strength-desc">在社交、数字娱乐、泛教育、电商等行业积累了丰富经验，对AIGC领域有项目落地经验</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工作经历 -->
            <div class="section">
                <h2 class="section-title">工作经历</h2>
                
                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">大模型负责人</div>
                            <div class="company">亚思未来科技</div>
                            <div class="keywords">数字人/AIGC/微调/RAG/语音克隆/语料库/电商直播</div>
                        </div>
                        <div class="job-duration">2023.07 - 至今</div>
                    </div>
                    <ul class="achievements">
                        <li>规划实施AIGC技术架构，适配电商直播实时互动场景</li>
                        <li>微调基座模型构建小语种电商模型，多个关键指标评估超GPT-4</li>
                        <li>融合开源TTS、LipSync、RAG等技术，打造数字人直播录播解决方案</li>
                        <li>开发代码编写Agent，涵盖chrome插件、客户端开发、数据库优化等功能</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">技术总监</div>
                            <div class="company">伟东云教育</div>
                            <div class="keywords">SaaS/技术中台/政企/AIGC/文生图/RAG/知识图谱</div>
                        </div>
                        <div class="job-duration">2017.11 - 2023.07</div>
                    </div>
                    <ul class="achievements">
                        <li>领导30+技术团队，制定产研流程与技术战略，保障高效交付与技术合规</li>
                        <li>主导云学堂产品从0搭建，用户注册数超千万，支持百万并发</li>
                        <li>推动技术中台建设，整合六大业务线，助力集团降本增效与上市合规</li>
                        <li>主导知识管理系统构建，融合大语言模型与RAG能力</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">高级架构师</div>
                            <div class="company">太合音乐（原百度音乐）</div>
                            <div class="keywords">音乐社交/高并发/稳定性/数学建模</div>
                        </div>
                        <div class="job-duration">2016.08 - 2017.11</div>
                    </div>
                    <ul class="achievements">
                        <li>主导百度音乐产品的整体技术架构规划，专注于系统稳定性和性能优化</li>
                        <li>主导搭建音乐价值链分析平台，建立数学模型优化版权采购决策</li>
                        <li>负责对外合作的产品技术对接，与百度搜索、百度百科等多方合作</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">技术总监</div>
                            <div class="company">明星衣橱</div>
                            <div class="keywords">电商/技术管理</div>
                        </div>
                        <div class="job-duration">2015.11 - 2016.08</div>
                    </div>
                    <ul class="achievements">
                        <li>负责50+人员技术管理和重点项目的研发</li>
                        <li>主导电商平台系统重构，引入微服务架构</li>
                        <li>主导搭建订单链路监控系统，将丢单率从15%+降至1%以内</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">架构师</div>
                            <div class="company">百度</div>
                            <div class="keywords">音乐社交/电商/高并发/稳定性/反作弊/数学建模</div>
                        </div>
                        <div class="job-duration">2010.03 - 2015.11</div>
                    </div>
                    <ul class="achievements">
                        <li>负责百度音乐、百度音乐人及票务平台等产品的架构设计</li>
                        <li>构建基于用户行为的PR模型，有效识别榜单作弊行为，获得百度音乐优秀项目奖</li>
                        <li>独立设计并实现消息订阅服务，支持千万级用户的高并发、低延时需求</li>
                        <li>开发Server降级模块，提升服务稳定性至99.9%以上，获得百度专利及优秀个人奖</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <div>
                            <div class="job-title">工程师</div>
                            <div class="company">新浪</div>
                            <div class="keywords">社交/高并发/稳定性</div>
                        </div>
                        <div class="job-duration">2008.07 - 2010.03</div>
                    </div>
                    <ul class="achievements">
                        <li>参与新浪圈子、新浪空间、新浪论坛和新浪邮箱等产品的研发</li>
                        <li>参与新浪论坛2.0升级改造，项目获得新浪最佳项目奖，日访问量达3000万</li>
                        <li>负责"好友买卖"、"抢车位"等网页游戏的开发，日访问量达百万量级</li>
                        <li>主导企业邮箱服务质量监控平台的搭建，实现故障识别效率提升至分钟级</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
