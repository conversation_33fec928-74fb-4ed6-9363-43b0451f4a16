<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试用期重点工作回顾</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 2px;
        }

        .category-section {
            margin: 50px 0;
        }

        .category-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 2px dashed #dee2e6;
        }

        .category-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .category-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .category-desc {
            color: #6c757d;
            font-size: 1.1em;
            line-height: 1.5;
        }

        .work-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .work-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .work-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        /* AI应用领域卡片样式 */
        .ai-card {
            border-left-color: #e74c3c;
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        }

        .ai-card:nth-child(2) {
            border-left-color: #9b59b6;
            background: linear-gradient(135deg, #f8f4ff 0%, #e9d5ff 100%);
        }

        .ai-card:nth-child(3) {
            border-left-color: #3498db;
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
        }

        .ai-card:nth-child(4) {
            border-left-color: #f39c12;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        /* 研发效能领域卡片样式 */
        .efficiency-card {
            border-left-color: #27ae60;
            background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
        }

        .efficiency-card:nth-child(2) {
            border-left-color: #17a2b8;
            background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
        }

        .work-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .work-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            color: white;
        }

        .ai-card .work-icon { background: #e74c3c; }
        .ai-card:nth-child(2) .work-icon { background: #9b59b6; }
        .ai-card:nth-child(3) .work-icon { background: #3498db; }
        .ai-card:nth-child(4) .work-icon { background: #f39c12; }
        .efficiency-card .work-icon { background: #27ae60; }
        .efficiency-card:nth-child(2) .work-icon { background: #17a2b8; }

        .work-achievements {
            list-style: none;
        }

        .work-achievements li {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
            color: #555;
            line-height: 1.5;
        }

        .work-achievements li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: 500;
        }

        .stats-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .work-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>试用期重点工作回顾</h1>
            <p>技术创新与业务价值并重的实践成果</p>
        </div>

        <div class="content">
            <h2 class="section-title">重点工作成果</h2>

            <div class="stats-section">
                <h3 style="margin-bottom: 20px; font-size: 1.5em;">试用期数据概览</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">2</div>
                        <div class="stat-label">核心领域</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">重点项目</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">MCP插件开发</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">任务完成率</div>
                    </div>
                </div>
            </div>

            <!-- AI应用领域 -->
            <div class="category-section">
                <div class="category-header">
                    <div class="category-icon">🤖</div>
                    <h3 class="category-title">AI应用领域</h3>
                    <p class="category-desc">AIGC技术在多模态能力、文档生成、内容创作等场景的深度应用</p>
                </div>

                <div class="work-grid">
                    <div class="work-card ai-card">
                        <div class="work-title">
                            <div class="work-icon">🎨</div>
                            M78 Agent 多模态能力建设
                        </div>
                        <ul class="work-achievements">
                            <li>搭建<span class="highlight">图像生成</span>、<span class="highlight">语音合成</span>、<span class="highlight">数字人</span>工作流</li>
                            <li>完成<span class="highlight">小红书文案创作</span>工作流开发</li>
                            <li>实现多模态能力的完整集成与优化</li>
                        </ul>
                    </div>

                    <div class="work-card ai-card">
                        <div class="work-title">
                            <div class="work-icon">🔌</div>
                            MCP（多能力平台）实践
                        </div>
                        <ul class="work-achievements">
                            <li>完成<span class="highlight">6个MCP插件</span>的开发与部署</li>
                            <li>设计<span class="highlight">stdio2sse协议转换器</span>满足远程调用需求</li>
                            <li>提升平台扩展性和插件生态建设</li>
                        </ul>
                    </div>

                    <div class="work-card ai-card">
                        <div class="work-title">
                            <div class="work-icon">�</div>
                            AI Doc 技术文档系统
                        </div>
                        <ul class="work-achievements">
                            <li>构建<span class="highlight">AI驱动的技术文档自动生成系统</span></li>
                            <li>打通<span class="highlight">MeeGo、飞书</span>等平台完整链路</li>
                            <li>集成<span class="highlight">RAG方案及Memory管理</span>优化</li>
                            <li>规划标准化工具和垂类模型建设</li>
                        </ul>
                    </div>

                    <div class="work-card ai-card">
                        <div class="work-title">
                            <div class="work-icon">📱</div>
                            小红书RPA自动化
                        </div>
                        <ul class="work-achievements">
                            <li>实现<span class="highlight">图文内容一键生成与推送</span></li>
                            <li>开发<span class="highlight">登录态同步助手</span>保障流程稳定性</li>
                            <li>显著提高运营内容产出效率</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 研发效能领域 -->
            <div class="category-section">
                <div class="category-header">
                    <div class="category-icon">⚡</div>
                    <h3 class="category-title">研发效能领域</h3>
                    <p class="category-desc">压测平台运维、代码质量监控等基础设施建设与效能提升</p>
                </div>

                <div class="work-grid">
                    <div class="work-card efficiency-card">
                        <div class="work-title">
                            <div class="work-icon">�</div>
                            压测平台运维
                        </div>
                        <ul class="work-achievements">
                            <li>熟悉压测平台流程，<span class="highlight">参与值班4次</span></li>
                            <li>识别并规划<span class="highlight">域名同步检测工具</span></li>
                            <li>优化<span class="highlight">Dubbo接口压测性能</span>方向规划</li>
                        </ul>
                    </div>

                    <div class="work-card efficiency-card">
                        <div class="work-title">
                            <div class="work-icon">�</div>
                            效能指标平台
                        </div>
                        <ul class="work-achievements">
                            <li>负责<span class="highlight">代码Sonar指标采集</span>及可视化</li>
                            <li>涵盖<span class="highlight">质量评分、复杂度、覆盖率</span>等关键指标</li>
                            <li>为团队代码质量提升提供数据支撑</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
