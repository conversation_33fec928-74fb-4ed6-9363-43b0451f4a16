<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作计划 - PPT展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 50px;
            text-align: center;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
        }

        .content {
            padding: 60px 40px;
        }

        .overview-section {
            text-align: center;
            margin-bottom: 60px;
        }

        .overview-title {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .overview-circle {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0 auto 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .overview-content {
            text-align: center;
            color: white;
        }

        .overview-number {
            font-size: 4em;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .overview-label {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .platforms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .platform-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .platform-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .platform-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .platform-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .platform-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            margin-right: 20px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .m78-card .platform-icon {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .aidoc-card .platform-icon {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .pressure-card .platform-icon {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .platform-title {
            font-size: 1.8em;
            font-weight: 600;
            color: #2c3e50;
        }

        .goals-container {
            position: relative;
        }

        .goals-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 8px solid #ecf0f1;
            position: relative;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .goals-center {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            text-align: center;
            font-size: 1.1em;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .goal-point {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            border: 4px solid;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 0.9em;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .m78-card .goal-point { border-color: #e74c3c; background: #e74c3c; }
        .aidoc-card .goal-point { border-color: #3498db; background: #3498db; }
        .pressure-card .goal-point { border-color: #27ae60; background: #27ae60; }

        .goal-point:nth-child(1) { top: -20px; left: 50%; transform: translateX(-50%); }
        .goal-point:nth-child(2) { top: 30%; right: -20px; }
        .goal-point:nth-child(3) { bottom: 30%; right: -20px; }
        .goal-point:nth-child(4) { bottom: -20px; left: 50%; transform: translateX(-50%); }
        .goal-point:nth-child(5) { bottom: 30%; left: -20px; }
        .goal-point:nth-child(6) { top: 30%; left: -20px; }

        .goals-list {
            list-style: none;
        }

        .goals-list li {
            margin-bottom: 15px;
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid;
            position: relative;
            transition: all 0.3s ease;
        }

        .goals-list li:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .m78-card .goals-list li { border-left-color: #e74c3c; }
        .aidoc-card .goals-list li { border-left-color: #3498db; }
        .pressure-card .goals-list li { border-left-color: #27ae60; }

        .goals-list li::before {
            content: "→";
            position: absolute;
            left: -2px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: bold;
        }

        .m78-card .goals-list li::before { color: #e74c3c; border: 2px solid #e74c3c; }
        .aidoc-card .goals-list li::before { color: #3498db; border: 2px solid #3498db; }
        .pressure-card .goals-list li::before { color: #27ae60; border: 2px solid #27ae60; }

        @media (max-width: 768px) {
            .platforms-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .platform-card {
                padding: 30px 20px;
            }
            
            .overview-circle {
                width: 250px;
                height: 250px;
            }
            
            .goals-circle {
                width: 150px;
                height: 150px;
            }
            
            .goals-center {
                width: 80px;
                height: 80px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>工作计划</h1>
            <p>技术创新与平台建设的战略规划</p>
        </div>

        <div class="content">
            <div class="overview-section">
                <h2 class="overview-title">核心平台规划</h2>
                <div class="overview-circle">
                    <div class="overview-content">
                        <div class="overview-number">3</div>
                        <div class="overview-label">重点平台</div>
                    </div>
                </div>
                <p style="font-size: 1.2em; color: #6c757d; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                    聚焦M78多模态平台、AI Doc文档平台、压测平台三大核心领域，
                    通过技术创新和能力提升，推动平台生态建设与业务价值实现
                </p>
            </div>

            <div class="platforms-grid">
                <!-- M78 平台 -->
                <div class="platform-card m78-card">
                    <div class="platform-header">
                        <div class="platform-icon">🤖</div>
                        <div class="platform-title">M78 平台</div>
                    </div>
                    
                    <div class="goals-container">
                        <div class="goals-circle">
                            <div class="goal-point">1</div>
                            <div class="goal-point">2</div>
                            <div class="goal-point">3</div>
                            <div class="goals-center">多模态<br>智能平台</div>
                        </div>
                        
                        <ul class="goals-list">
                            <li>持续深化多模态模型集成，强化跨模态理解与生成能力</li>
                            <li>构建高效、稳定的自动化工作流，提升开发与应用效率</li>
                            <li>优化RAG技术，增强知识融合和信息推理能力</li>
                        </ul>
                    </div>
                </div>

                <!-- AI Doc 平台 -->
                <div class="platform-card aidoc-card">
                    <div class="platform-header">
                        <div class="platform-icon">📝</div>
                        <div class="platform-title">AI Doc 平台</div>
                    </div>
                    
                    <div class="goals-container">
                        <div class="goals-circle">
                            <div class="goal-point">1</div>
                            <div class="goal-point">2</div>
                            <div class="goal-point">3</div>
                            <div class="goal-point">4</div>
                            <div class="goals-center">智能文档<br>生成平台</div>
                        </div>
                        
                        <ul class="goals-list">
                            <li>融合多模态，增强文档理解能力</li>
                            <li>探索Memory机制，突破上下文限制，解决信息遗忘与窗口爆炸问题</li>
                            <li>引入MCP能力，拓展AI模型的能力边界，增强平台扩展性</li>
                            <li>训练垂类专业模型，提升技术文档生成质量，推动标准化流程工具落地</li>
                        </ul>
                    </div>
                </div>

                <!-- 压测平台 -->
                <div class="platform-card pressure-card">
                    <div class="platform-header">
                        <div class="platform-icon">⚡</div>
                        <div class="platform-title">压测平台</div>
                    </div>
                    
                    <div class="goals-container">
                        <div class="goals-circle">
                            <div class="goal-point">1</div>
                            <div class="goal-point">2</div>
                            <div class="goals-center">性能测试<br>优化平台</div>
                        </div>
                        
                        <ul class="goals-list">
                            <li>解决域名同步问题，确保测试流程稳定</li>
                            <li>持续优化平台性能与稳定性</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
